package modele;

/**
 * Le plan de jeu est la classe qui supporte le modèle du programme.
 * Il contient:
 * 	- le donjon
 *  - le joueur
 *  - les créatures
 *  
 * et actionne les mécaniques du jeu.
 * 
 * Le plan de jeu est implémenté en Lazy Singleton
 * 
 * Ajouts : Section 3-4 (créatures, génération, interaction)
 * 
 * <AUTHOR> | ETS
 * @version Hiver 2022 - TP2
 */
 

import java.util.Random;
import java.util.Vector;
import java.util.concurrent.TimeUnit;

import donjon.Case;
import donjon.Configuration;
import donjon.Donjon;
import observer.MonObservable;
import observer.MonObserver;
import personnage.AbstractCreature;
import personnage.Araignee;
import personnage.Dragon;
import personnage.Heros;
import personnage.Minotaure;
import physique.Position;

public class PlanDeJeu extends MonObservable implements MonObserver, Runnable {

	private Vector<AbstractCreature> creatures;
	private Donjon donjon;
	private Heros heros;

	private boolean partieEnCours = false;
	private int niveauCourant = 0;
	private Random rand = new Random(System.currentTimeMillis());

	private static final PlanDeJeu instance = new PlanDeJeu();
	private static Thread t;

	/**
	 * constructeur du plan de jeu
	 */
	public PlanDeJeu(){
		partieEnCours = true;
		nouveauNiveau();
	}

	/**
	 * méthode pour obtenir une référence au plan de jeu
	 * @return l'instance
	 */
	public static PlanDeJeu getInstance(){
		return instance;
	}

	/**
	 * méthode pour obtenir une référence au donjon
	 * @return référence au donjon
	 */
	public Donjon getDonjon(){
		return this.donjon;
	}

	/**
	 * méthode pour obtenir une référence au héros
	 */
	public Heros getHeros() {
		return this.heros;
	}

	/**
	 * méthode pour obtenir les créatures
	 */
	public Vector<AbstractCreature> getCreatures() {
		return this.creatures;
	}

	@Override
	public void avertir() {
		validerEtatJeu();
		this.avertirLesObservers();
	}

	@Override
	public void run() {
		while(partieEnCours){
			for (AbstractCreature creature : creatures) {
				creature.seDeplacer();
			}
			this.avertirLesObservers();

			try {
				TimeUnit.SECONDS.sleep(2);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * méthode qui valide les règles du jeu
	 */
	private void  validerEtatJeu(){
		// TODO : Gestion de l’équipement, des combats, de la mort, etc.
	}

	/**
	 * méthode qui lance un nouveau niveau
	 */
	private void  nouveauNiveau(){
		partieEnCours = true;
		this.donjon = new Donjon();

		// Crée le héros (s'il n'existe pas encore)
		if (heros == null)
			heros = new Heros("Héros");

		// Le place à la position de départ du donjon
		heros.setCase(donjon.getDepart());

		// Crée les créatures
		initCreatures();

		// Démarre la tâche de fond si ce n'est pas déjà fait
		if (t == null) {
			t = new Thread(this);
			t.start();
		}

		this.avertirLesObservers();
	}

	private void initCreatures() {
		Configuration config = Configuration.getInstance();
		creatures = new Vector<>();
		Case[][] grille = donjon.getGrille();

		for (int i = 0; i < config.getNbCreatures(); i++) {
			AbstractCreature creature;
			int type = rand.nextInt(3);

			if (type == 0)
				creature = new Minotaure("Minotaure " + i);
			else if (type == 1)
				creature = new Dragon("Dragon " + i);
			else
				creature = new Araignee("Araignée " + i);

			Position pos = donjon.getPositionAlea();
			creature.setCase(grille[pos.getI()][pos.getJ()]);
			creature.attacherObserver(this);
			creatures.add(creature);
		}
	}

	/**
	 * méthode qui gère une partie gagnée
	 */
	private void partieGagne(){
		niveauCourant++;

		Configuration config = Configuration.getInstance();
		int nbCols = (int)config.getConfig(Configuration.NB_COLONNES);
		int nbLignes = (int)config.getConfig(Configuration.NB_LIGNES);
		int nbCreatures = (int)config.getConfig(Configuration.NB_CREATURES);

		config.setConfig(Configuration.NB_COLONNES, nbCols+1);
		config.setConfig(Configuration.NB_LIGNES, nbLignes+1);
		config.setConfig(Configuration.NB_CREATURES, nbCreatures+2);

		nouveauNiveau();
	}

	/**
	 * gestion d'une partie perdue
	 */
	private void partiePerdu(){
		partieEnCours = false;

		try {
			t.join();
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		Configuration.remiseAZero();
	}
}
