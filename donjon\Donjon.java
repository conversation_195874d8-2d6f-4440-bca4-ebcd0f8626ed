package donjon;

import physique.*;
import pile.PileSChainee;
import java.util.Random;

public class Donjon {
    private Case[][] grille;
    private Case depart, fin;
    private Random rand;

    public Donjon() {
        Configuration config = Configuration.getInstance();
        grille = new Case[config.getDimI()][config.getDimJ()];
        rand = new Random();

        for (int i = 0; i < config.getDimI(); i++) {
            for (int j = 0; j < config.getDimJ(); j++) {
                grille[i][j] = new Case(new Position(i, j));
            }
        }

        Position posDepart = getPositionAlea();
        depart = grille[posDepart.getI()][posDepart.getJ()];

        produireLabyrinthe(); // génération automatique
        fin.setFin(); // la dernière case empilée devient la sortie
    }

    public Case getDepart() {
        return depart;
    }

    public Case getFin() {
        return fin;
    }

    public Case[][] getGrille() {
        return grille;
    }

    public Position getPositionAlea() {
        Configuration config = Configuration.getInstance();
        int i = rand.nextInt(config.getDimI());
        int j = rand.nextInt(config.getDimJ());
        return new Position(i, j);
    }

    public int getNbVoisinsNonDeveloppe(Position pos) {
        int count = 0;
        Direction dir = new Direction();

        for (int d = 0; d < 4; d++) {
            Position dirPos = dir.directionAPosition(d);
            Position voisinPos = new Position(pos);
            voisinPos.additionnerPos(dirPos);

            if (estValide(voisinPos)) {
                if (!grille[voisinPos.getI()][voisinPos.getJ()].getDeveloppe()) {
                    count++;
                }
            }
        }

        return count;
    }

    private boolean estValide(Position pos) {
        Configuration config = Configuration.getInstance();
        return pos.getI() >= 0 && pos.getI() < config.getDimI()
            && pos.getJ() >= 0 && pos.getJ() < config.getDimJ();
    }

    public Position getVoisinAlea(Position pos) {
        Direction dir = new Direction();
        Position voisinPos;

        do {
            int d = dir.obtenirDirAlea();
            Position dirPos = dir.directionAPosition(d);
            voisinPos = new Position(pos);
            voisinPos.additionnerPos(dirPos);
        } while (!estValide(voisinPos));

        return voisinPos;
    }

    public Position getVoisinLibreAlea(Position pos) {
        Direction dir = new Direction();
        Position voisinPos;
        Case voisinCase;

        do {
            int d = dir.obtenirDirAlea();
            Position dirPos = dir.directionAPosition(d);
            voisinPos = new Position(pos);
            voisinPos.additionnerPos(dirPos);

            if (!estValide(voisinPos)) continue;

            voisinCase = grille[voisinPos.getI()][voisinPos.getJ()];
        } while (!estValide(voisinPos) || voisinCase.getDeveloppe());

        return voisinPos;
    }

    public void produireLabyrinthe() {
        PileSChainee<Case> pile = new PileSChainee<>();
        pile.empiler(depart);

        Direction direction = new Direction();

        while (!pile.estVide()) {
            Case courante = pile.regarder();
            Position pos = courante.getPosition();
            courante.setDeveloppe(true);

            if (getNbVoisinsNonDeveloppe(pos) > 0) {
                Position voisinPos = getVoisinLibreAlea(pos);
                Case voisin = grille[voisinPos.getI()][voisinPos.getJ()];

                Position vecteur = new Position(voisinPos);
                vecteur.soustrairePos(pos);
                int dir = direction.positionADirection(vecteur);

                courante.setVoisin(dir, voisin);
                voisin.setVoisin(direction.directionOpposee(dir), courante);

                pile.empiler(voisin);
                fin = voisin;
            } else {
                pile.depiler();
            }
        }
    }
}